"use client";

import React, { useState, useRef } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import CatalogHierarchical from "../../components/CatalogHierarchical";

const TenderFormContainer = styled.div`
  background-color: white;
  min-height: calc(100vh - 60px);
  padding: 24px 20px;

  @media (max-width: 768px) {
    padding: 16px;
    min-height: calc(100vh - 65px);
  }
`;
TenderFormContainer.displayName = "TenderFormContainer";

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
ContentContainer.displayName = "ContentContainer";

const Header = styled.div`
  border-bottom: 1px solid #dfe4e5;
  /* margin-bottom: 24px; */
`;
Header.displayName = "Header";

const HeaderContent = styled.div`
  margin: 0 auto;
  max-width: 1150px;
  display: flex;
  padding: 22px 40px 16px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;

  @media (max-width: 768px) {
    padding: 16px 20px;
  }
`;
HeaderContent.displayName = "HeaderContent";

const BackButton = styled.button`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: white;
  color: #434a54;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  gap: 8px;
  padding: 8px 20px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
BackButton.displayName = "BackButton";

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;
`;
ActionButtons.displayName = "ActionButtons";

const ClearAllButton = styled.button`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: white;
  color: #434a54;
  border: 1px solid #d6dce1;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
ClearAllButton.displayName = "ClearAllButton";

const CreateTenderButton = styled.button`
  background-color: #0066cc;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #0055b3;
  }
`;
CreateTenderButton.displayName = "CreateTenderButton";

const Title = styled.h1`
  font-size: 38px;
  font-weight: 600;
  line-height: 1.5;
  color: #434a54;
  margin-bottom: 16px;
  margin-top: 32px;

  @media (max-width: 768px) {
    font-size: 24px;
  }
`;
Title.displayName = "Title";

const SectionTitle = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: #434a54;
  margin-top: 42px;
  margin-bottom: 24px;

  &:first-of-type {
    margin-top: 0;
  }
`;
SectionTitle.displayName = "SectionTitle";

const Text = styled.p`
  font-size: 17px;
  font-weight: 400;
  color: #434a54;
  margin-bottom: 16px;
  line-height: 1.5;
`;
Text.displayName = "Text";

const FormSection = styled.div`
  background: #f5f5f5;
  border-radius: 4px;
  padding: 24px;
  margin-bottom: 24px;
`;
FormSection.displayName = "FormSection";

const FormRow = styled.div`
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  align-items: flex-end;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
`;
FormRow.displayName = "FormRow";

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
`;
FormGroup.displayName = "FormGroup";

const Label = styled.div`
  font-size: 14px;
  color: #969ea7;
  margin-bottom: 10px;

  @media (max-width: 768px) {
    font-size: 17px;
  }
`;
Label.displayName = "Label";

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  height: 36px;
  width: 100%;

  &:focus {
    border-color: #0066cc;
    outline: none;
  }
`;
Input.displayName = "Input";

const TextArea = styled.textarea`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  width: 100%;
  min-height: 100px;
  resize: vertical;

  &:focus {
    border-color: #0066cc;
    outline: none;
  }
`;
TextArea.displayName = "TextArea";

const CatalogSection = styled.div`
  margin-bottom: 24px;
`;
CatalogSection.displayName = "CatalogSection";

const RegionSection = styled.div`
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
`;
RegionSection.displayName = "RegionSection";

const CityDropdown = styled.div`
  position: relative;
  width: 300px;
`;
CityDropdown.displayName = "CityDropdown";

const CityButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  cursor: pointer;
  height: 36px;

  &:focus {
    border-color: #0066cc;
    outline: none;
  }

  img {
    transition: transform 0.3s ease;
    transform: ${(props) => (props.isOpen ? "rotate(180deg)" : "rotate(0)")};
  }
`;
CityButton.displayName = "CityButton";

const CityDropdownList = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 4px;
`;
CityDropdownList.displayName = "CityDropdownList";

const CityDropdownItem = styled.div`
  padding: 12px 16px;
  border-bottom: 1px solid #f1f3f4;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f8f9fa;
  }
`;
CityDropdownItem.displayName = "CityDropdownItem";

const CityCheckbox = styled.div`
  width: 16px;
  height: 16px;
  border: 2px solid ${(props) => (props.checked ? "#0066cc" : "#ddd")};
  border-radius: 3px;
  background-color: ${(props) => (props.checked ? "#0066cc" : "white")};
  position: relative;

  ${(props) =>
    props.checked &&
    `
    &::after {
      content: '✓';
      position: absolute;
      top: -2px;
      left: 1px;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
  `}
`;
CityCheckbox.displayName = "CityCheckbox";

const UploadButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #434a54;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
UploadButton.displayName = "UploadButton";

const UploadText = styled.span`
  font-size: 14px;
  color: #434a54;
`;
UploadText.displayName = "UploadText";

const HiddenFileInput = styled.input`
  display: none;
`;
HiddenFileInput.displayName = "HiddenFileInput";

const AttachedFilesList = styled.div`
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
`;
AttachedFilesList.displayName = "AttachedFilesList";

const AttachedFileItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
`;
AttachedFileItem.displayName = "AttachedFileItem";

const FileInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;
`;
FileInfo.displayName = "FileInfo";

const FileName = styled.span`
  font-size: 14px;
  color: #333;
  font-weight: 500;
`;
FileName.displayName = "FileName";

const FileSize = styled.span`
  font-size: 12px;
  color: #6c757d;
`;
FileSize.displayName = "FileSize";

const RemoveFileButton = styled.button`
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
  }
`;
RemoveFileButton.displayName = "RemoveFileButton";

const AddMaterialClient = () => {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);
  const [selectedRegionId, setSelectedRegionId] = useState("02"); // По умолчанию Алматы
  const [formData, setFormData] = useState({
    materialName: "",
    unit: "",
    description: "",
    retailPrice: "",
    wholesalePrice: "",
    wholesaleFromQty: "",
    region: "Алматы", // По умолчанию
    files: [],
  });

  // Данные регионов Казахстана
  const regions = [
    { RegionId: "01", RegionName: "Нур-Султан" },
    { RegionId: "02", RegionName: "Алматы" },
    { RegionId: "03", RegionName: "Шымкент" },
    { RegionId: "04", RegionName: "Акмолинская область" },
    { RegionId: "05", RegionName: "Актюбинская область" },
    { RegionId: "06", RegionName: "Алматинская область" },
    { RegionId: "07", RegionName: "Атырауская область" },
    { RegionId: "08", RegionName: "Восточно-Казахстанская область" },
    { RegionId: "09", RegionName: "Жамбылская область" },
    { RegionId: "10", RegionName: "Западно-Казахстанская область" },
    { RegionId: "11", RegionName: "Карагандинская область" },
    { RegionId: "12", RegionName: "Костанайская область" },
    { RegionId: "13", RegionName: "Кызылординская область" },
    { RegionId: "14", RegionName: "Мангистауская область" },
    { RegionId: "15", RegionName: "Павлодарская область" },
    { RegionId: "16", RegionName: "Северо-Казахстанская область" },
    { RegionId: "17", RegionName: "Туркестанская область" },
  ];

  const handleBack = () => {
    router.push("/products/page/1?source=price-proposal");
  };

  const handleCategorySelect = (category) => {
    setSelectedCategory(category);
    console.log("Выбрана категория:", category);
  };

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleClearAll = () => {
    setFormData({
      materialName: "",
      unit: "",
      description: "",
      retailPrice: "",
      wholesalePrice: "",
      wholesaleFromQty: "",
      region: "Алматы",
      files: [],
    });
    setSelectedCategory(null);
  };

  const toggleCityDropdown = () => {
    setIsCityDropdownOpen(!isCityDropdownOpen);
  };

  const handleRegionSelect = (selectedRegion) => {
    setFormData((prev) => ({
      ...prev,
      region: selectedRegion.RegionName,
    }));
    setSelectedRegionId(selectedRegion.RegionId);
    setIsCityDropdownOpen(false);
  };

  // Получаем текущий выбранный регион
  const getCurrentRegion = () => {
    return (
      regions.find((region) => region.RegionId === selectedRegionId) ||
      regions[1]
    ); // По умолчанию Алматы
  };

  const handleFileUpload = (event) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const fileArray = Array.from(files);

      // Проверяем размер файлов (максимум 10MB на файл)
      const maxSize = 10 * 1024 * 1024; // 10MB
      const oversizedFiles = fileArray.filter((file) => file.size > maxSize);

      if (oversizedFiles.length > 0) {
        alert(
          `Файлы слишком большие. Максимальный размер: 10MB\n${oversizedFiles
            .map((f) => f.name)
            .join("\n")}`
        );
        return;
      }

      setFormData((prev) => ({
        ...prev,
        files: [...prev.files, ...fileArray],
      }));
    }
  };

  const handleRemoveFile = (fileIndex) => {
    setFormData((prev) => ({
      ...prev,
      files: prev.files.filter((_, i) => i !== fileIndex),
    }));
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleSubmit = () => {
    // Здесь будет логика отправки заявки
    // Пока просто показываем сообщение
    console.log("Отправка заявки:", {
      category: selectedCategory,
      formData: formData,
    });
    alert("Заявка отправлена! (пока без API)");
  };

  return (
    <>
      <Header>
        <HeaderContent>
          <BackButton onClick={handleBack}>
            <img src="/icons/arrow_back_24px.svg" alt="Назад" />
            НАЗАД К ВЫБОРУ ТОВАРОВ
          </BackButton>
          <ActionButtons>
            <ClearAllButton onClick={handleClearAll}>
              <img
                src="/icons/BusketCreateTender.svg"
                width={"13"}
                height={"13"}
              />
              ОЧИСТИТЬ ВСЕ
            </ClearAllButton>
          </ActionButtons>
        </HeaderContent>
      </Header>

      <TenderFormContainer>
        <ContentContainer>
          <Title>Подать заявку на добавление материала</Title>
          <Text>
            Заполните информацию о материале, который хотите добавить в каталог
          </Text>

          {/* Выбор каталога */}
          <SectionTitle>Выбор категории</SectionTitle>
          <CatalogSection>
            <CatalogHierarchical onCategorySelect={handleCategorySelect} />
            {selectedCategory && (
              <div
                style={{
                  marginTop: "16px",
                  padding: "12px",
                  backgroundColor: "#e8f4fd",
                  borderRadius: "4px",
                }}
              >
                <strong>Выбрана категория:</strong> <br />
                {selectedCategory.departmentName}
                {selectedCategory.sectionName &&
                  ` → ${selectedCategory.sectionName}`}
                {selectedCategory.subsectionName &&
                  ` → ${selectedCategory.subsectionName}`}
                {selectedCategory.groupName &&
                  ` → ${selectedCategory.groupName}`}
              </div>
            )}
          </CatalogSection>

          {/* Основная информация о материале */}
          <SectionTitle>Информация о материале</SectionTitle>
          <FormSection>
            <FormRow>
              <FormGroup>
                <Label>Наименование материала *</Label>
                <Input
                  type="text"
                  value={formData.materialName}
                  onChange={(e) =>
                    handleInputChange("materialName", e.target.value)
                  }
                  placeholder="Введите название материала"
                />
              </FormGroup>
              <FormGroup>
                <Label>Единица измерения *</Label>
                <Input
                  type="text"
                  value={formData.unit}
                  onChange={(e) => handleInputChange("unit", e.target.value)}
                  placeholder="шт, м, кг и т.д."
                />
              </FormGroup>
            </FormRow>

            <FormRow>
              <FormGroup>
                <Label>Описание</Label>
                <TextArea
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange("description", e.target.value)
                  }
                  placeholder="Подробное описание материала"
                />
              </FormGroup>
            </FormRow>
          </FormSection>

          {/* Ценовая информация */}
          <SectionTitle>Ценовая информация</SectionTitle>
          <FormSection>
            <FormRow>
              <FormGroup>
                <Label>Розничная цена (тг)</Label>
                <Input
                  type="number"
                  value={formData.retailPrice}
                  onChange={(e) =>
                    handleInputChange("retailPrice", e.target.value)
                  }
                  placeholder="0"
                />
              </FormGroup>
              <FormGroup>
                <Label>Оптовая цена (тг)</Label>
                <Input
                  type="number"
                  value={formData.wholesalePrice}
                  onChange={(e) =>
                    handleInputChange("wholesalePrice", e.target.value)
                  }
                  placeholder="0"
                />
              </FormGroup>
              <FormGroup>
                <Label>Оптом от количества</Label>
                <Input
                  type="number"
                  value={formData.wholesaleFromQty}
                  onChange={(e) =>
                    handleInputChange("wholesaleFromQty", e.target.value)
                  }
                  placeholder="0"
                />
              </FormGroup>
            </FormRow>
          </FormSection>

          {/* Регион */}
          <SectionTitle>Регион</SectionTitle>
          <RegionSection>
            <FormRow>
              <FormGroup>
                <Label>Регион поставки</Label>
                <CityDropdown>
                  <CityButton
                    onClick={toggleCityDropdown}
                    isOpen={isCityDropdownOpen}
                  >
                    {getCurrentRegion().RegionName}
                    <span
                      style={{
                        transform: isCityDropdownOpen
                          ? "rotate(180deg)"
                          : "rotate(0deg)",
                        transition: "transform 0.3s ease",
                      }}
                    >
                      ▼
                    </span>
                  </CityButton>
                  {isCityDropdownOpen && (
                    <CityDropdownList>
                      {regions.map((region) => (
                        <CityDropdownItem
                          key={region.RegionId}
                          onClick={() => handleRegionSelect(region)}
                        >
                          <span>{region.RegionName}</span>
                          <CityCheckbox
                            checked={selectedRegionId === region.RegionId}
                          />
                        </CityDropdownItem>
                      ))}
                    </CityDropdownList>
                  )}
                </CityDropdown>
              </FormGroup>
            </FormRow>
          </RegionSection>

          {/* Прикрепление файлов */}
          <SectionTitle>Прикрепление файлов</SectionTitle>
          <FormSection>
            <div>
              <Label>Прикрепить файлы (фото, документы)</Label>
              <UploadButton
                onClick={() => document.getElementById("file-input").click()}
                type="button"
              >
                <img src="/icons/Upload.svg" alt="Загрузить" />
                <UploadText>Прикрепить файл</UploadText>
              </UploadButton>

              <HiddenFileInput
                id="file-input"
                type="file"
                multiple
                onChange={handleFileUpload}
              />

              {formData.files && formData.files.length > 0 && (
                <AttachedFilesList>
                  {formData.files.map((file, fileIndex) => (
                    <AttachedFileItem key={fileIndex}>
                      <FileInfo>
                        <FileName>{file.name}</FileName>
                        <FileSize>{formatFileSize(file.size)}</FileSize>
                      </FileInfo>
                      <RemoveFileButton
                        onClick={() => handleRemoveFile(fileIndex)}
                        title="Удалить файл"
                      >
                        ×
                      </RemoveFileButton>
                    </AttachedFileItem>
                  ))}
                </AttachedFilesList>
              )}
            </div>
          </FormSection>

          {/* Кнопка отправки */}
          <div style={{ textAlign: "center", marginTop: "32px" }}>
            <CreateTenderButton
              onClick={handleSubmit}
              style={{ padding: "16px 32px", fontSize: "16px" }}
            >
              ОТПРАВИТЬ ЗАЯВКУ
              <img
                src="/icons/CheckCreateTender.svg"
                width={"15"}
                height={"15"}
                alt="Отправить"
              />
            </CreateTenderButton>
          </div>
        </ContentContainer>
      </TenderFormContainer>
    </>
  );
};

export default AddMaterialClient;
