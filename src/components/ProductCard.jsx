"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import styled from "styled-components";
import { useCart } from "../context/CartContext";
import { useProductImage } from "../hooks/useProductImage";
import { useAveragePricesContext } from "../context/AveragePricesContext";
import { useAuth } from "../context/AuthContext";

const Card = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%; /* Занимает всю доступную высоту */

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;
Card.displayName = "Card";

const ProductLink = styled(Link)`
  text-decoration: none;
  color: inherit;
  display: block;
  cursor: pointer;
`;
ProductLink.displayName = "ProductLink";

const ImageContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 160px; /* Фиксированная высота */
  padding: 12px;
  background-color: ${(props) => (props.isLoading ? "#f5f5f5" : "transparent")};
  position: relative;

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    opacity: ${(props) => (props.isLoading ? 0.5 : 1)};
  }
`;
ImageContainer.displayName = "ImageContainer";

const Title = styled.h3`
  font-size: 17px;
  font-weight: bold;
  color: #333;
  line-height: 150%;
  margin-top: 16px;
  min-height: 50px; /* Минимальная высота вместо фиксированной */
  overflow: visible; /* Показываем весь текст */
  display: block; /* Убираем ограничение webkit-box */
`;
Title.displayName = "Title";

const PriceInfo = styled.div`
  margin-top: 24px;
  flex: 0 0 auto; /* Не растягивается */
`;
PriceInfo.displayName = "PriceInfo";

const PriceRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 14px;
  color: #808185;
  border-bottom: 1px solid #ddd;
  padding-bottom: 8px;
`;
PriceRow.displayName = "PriceRow";

const Price = styled.span`
  font-weight: ${(props) => (props.bold ? "600" : "400")};
`;
Price.displayName = "Price";

const ButtonContainer = styled.div`
  margin-top: auto; /* Прижимает кнопки к низу карточки */
  padding-top: 16px; /* Добавляет отступ сверху */
  display: flex;
  /* gap: 8px; */
  justify-content: space-between;
  @media (max-width: 480px) {
    flex-direction: column;
    gap: 10px;
  }
`;
ButtonContainer.displayName = "ButtonContainer";

const OrderButton = styled.button`
  padding: 6px 10px;
  background-color: white;
  font-weight: bold;
  color: #333;
  border: 2px solid #d6dce1;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    background-color: #f5f5f5;
  }
`;
OrderButton.displayName = "OrderButton";

const CartButton = styled.button`
  padding: 6px 10px;
  border-radius: 8px;
  font-weight: 600;
  border: none;
  cursor: ${(props) => (props.disabled ? "not-allowed" : "pointer")};
  background-color: ${(props) => (props.disabled ? "#9ca3af" : "#0066cc")};
  color: white;
  opacity: ${(props) => (props.disabled ? 0.6 : 1)};

  &:hover {
    background-color: ${(props) => (props.disabled ? "#9ca3af" : "#0055b3")};
  }
`;
CartButton.displayName = "CartButton";

const ProductCard = ({ product }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { addToCart, isInCart, getItemQuantity } = useCart();
  const { imageUrl, isLoading } = useProductImage(product.MaterialId);
  const { getPriceByMaterialId } = useAveragePricesContext();
  const { user, isAuthenticated } = useAuth();

  // Проверяем, пришел ли пользователь для подачи ценового предложения
  const [isPriceProposalMode, setIsPriceProposalMode] = useState(false);

  useEffect(() => {
    // Проверяем URL параметр
    const source = searchParams.get("source");
    if (source === "price-proposal") {
      setIsPriceProposalMode(true);
    }
  }, [searchParams]);

  // Получаем средние цены для данного материала
  const averagePrice = getPriceByMaterialId(product.MaterialId);

  // Проверяем, есть ли товар в корзине
  const productInCart = isInCart(product.MaterialId);
  const quantityInCart = getItemQuantity(product.MaterialId);

  const handleAddToCart = () => {
    // Если товар уже в корзине, не добавляем его снова
    if (productInCart) {
      return;
    }

    // Создаем объект товара с правильными именами свойств для корзины
    const cartItem = {
      id: product.MaterialId,
      title: product.MaterialName,
      retailPrice: averagePrice?.AvgRetailPrice
        ? parseFloat(averagePrice.AvgRetailPrice)
        : null,
      wholesalePrice: averagePrice?.AvgTradePrice
        ? parseFloat(averagePrice.AvgTradePrice)
        : null,
      image: imageUrl,
      // Товары из каталога используют средние цены
      isFromSpecificSupplier: false,
    };

    addToCart(cartItem);
  };

  const handleOrderNow = () => {
    // Создаем объект товара с правильными именами свойств для корзины
    const cartItem = {
      id: product.MaterialId,
      title: product.MaterialName,
      retailPrice: averagePrice?.AvgRetailPrice
        ? parseFloat(averagePrice.AvgRetailPrice)
        : null,
      wholesalePrice: averagePrice?.AvgTradePrice
        ? parseFloat(averagePrice.AvgTradePrice)
        : null,
      image: imageUrl,
      // Товары из каталога используют средние цены
      isFromSpecificSupplier: false,
    };

    // Добавляем товар в корзину и перенаправляем на страницу корзины
    addToCart(cartItem);
    router.push("/cart");
  };

  const handleSubmitPrice = async () => {
    // Проверяем авторизацию
    if (!isAuthenticated) {
      router.push("/auth?from=tender");
      return;
    }

    // Проверяем companyId
    if (
      !user?.userId ||
      user?.userId === "00000000-0000-0000-0000-000000000000" ||
      !user?.companyId ||
      user?.companyId === "0000" ||
      user?.companyId === 0
    ) {
      router.push("/auth?from=company");
      return;
    }

    try {
      // Подготавливаем данные товара для ценового предложения
      const productData = {
        MaterialId: product.MaterialId,
        MaterialName: product.MaterialName,
        UnitId: product.UnitId,
      };

      // Сохраняем товар в localStorage для PriceProposalFormClient
      localStorage.setItem(
        "selectedPriceProposalProducts",
        JSON.stringify([productData])
      );

      // Получаем средние цены для автозаполнения
      const response = await fetch(
        `https://api.sadi.kz/api/Adverts/AveragePrices?materialIds=${product.MaterialId}`
      );

      if (response.ok) {
        const averagePrices = await response.json();
        localStorage.setItem(
          "proposalAveragePrices",
          JSON.stringify(averagePrices)
        );
      } else {
        // Очищаем старые данные о ценах
        localStorage.removeItem("proposalAveragePrices");
      }
    } catch (error) {
      console.error("Ошибка при получении рекомендуемых цен:", error);
      // Очищаем старые данные о ценах
      localStorage.removeItem("proposalAveragePrices");
    }

    // Переходим на страницу с формой ценового предложения
    router.push("/price-proposal-form");
  };

  return (
    <Card as="article">
      <ProductLink href={`/product/${product.MaterialId}`}>
        <ImageContainer isLoading={isLoading}>
          <img
            src={imageUrl}
            alt={product.MaterialName}
            loading="lazy" // Ленивая загрузка изображений
            decoding="async" // Асинхронное декодирование изображений
            onError={(e) => {
              e.target.onerror = null; // Предотвращаем бесконечную рекурсию
              e.target.src = "/images/placeholder.png"; // Заглушка при ошибке
            }}
          />
        </ImageContainer>

        <Title as="h2">{product.MaterialName}</Title>

        <PriceInfo>
          <PriceRow>
            <span>Единица измерения:</span>
            <Price bold> {product.UnitId}</Price>
          </PriceRow>
          <PriceRow>
            <span>Сред. розн.:</span>
            <Price bold>
              {averagePrice?.AvgRetailPrice
                ? `${averagePrice.AvgRetailPrice} ₸`
                : "По запросу"}
            </Price>
          </PriceRow>
          <PriceRow>
            <span>Сред. опт.:</span>
            <Price>
              {averagePrice?.AvgTradePrice
                ? `${averagePrice.AvgTradePrice} ₸`
                : "По запросу"}
            </Price>
          </PriceRow>
          <PriceRow>
            <span>Кол-во поставщиков</span>
            <Price bold>{averagePrice?.CompanyCnt || 0}</Price>
          </PriceRow>
        </PriceInfo>
      </ProductLink>

      <ButtonContainer>
        {isPriceProposalMode ? (
          <OrderButton
            onClick={handleSubmitPrice}
            aria-label={`Подать цену на ${product.MaterialName}`}
          >
            Подать цену
          </OrderButton>
        ) : (
          <OrderButton
            onClick={handleOrderNow}
            aria-label={`Оформить заказ на ${product.MaterialName}`}
          >
            Оформить сейчас
          </OrderButton>
        )}
        <CartButton
          onClick={handleAddToCart}
          disabled={productInCart}
          aria-label={
            productInCart
              ? `${product.MaterialName} уже в корзине (${quantityInCart} шт.)`
              : `Добавить ${product.MaterialName} в корзину`
          }
        >
          {productInCart ? `В корзине (${quantityInCart})` : "В корзину"}
        </CartButton>
      </ButtonContainer>
    </Card>
  );
};

ProductCard.displayName = "ProductCard";

export default ProductCard;
