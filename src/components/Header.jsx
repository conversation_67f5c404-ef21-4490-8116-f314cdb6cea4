"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import styled from "styled-components";
import { useCart } from "../context/CartContext";
import { useAuth } from "../context/AuthContext";

const HeaderContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
`;
HeaderContainer.displayName = "HeaderContainer";

const RegistryLink = styled(Link)`
  font-size: 17px;
  color: #333;
  text-decoration: none;
  display: flex;
  align-items: center;

  img {
    margin-right: 8px;
  }
`;
RegistryLink.displayName = "RegistryLink";

const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;
RightSection.displayName = "RightSection";

const SupportLink = styled.a`
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: bold;
  color: #333;
  border-radius: 8px;
  padding: 6px 12px;
  border: 2px solid #d6dce1;

  img {
    margin-right: 8px;
  }
`;
SupportLink.displayName = "SupportLink";

const AddPriceButton = styled.button`
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  background-color: #0066cc;
  color: white;
  display: flex;
  align-items: center;
  font-weight: bold;
  border: none;
  cursor: pointer;

  &:hover {
    background-color: #0055b3;
  }

  img {
    margin-right: 8px;
  }
`;
AddPriceButton.displayName = "AddPriceButton";

const CartButton = styled.button`
  background: none;
  border: none;
  position: relative;
  cursor: pointer;
  border: 2px solid #d6dce1;
  border-radius: 8px;
  padding: 2px 2px 0;

  svg {
    width: 24px;
    height: 24px;
  }

  span {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #0066cc;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
`;
CartButton.displayName = "CartButton";

const Header = ({ onCartClick }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { getCartItemsCount } = useCart();
  const { user, isAuthenticated } = useAuth();

  // Проверяем, находимся ли мы в режиме подачи ценового предложения
  const [isPriceProposalMode, setIsPriceProposalMode] = useState(false);

  useEffect(() => {
    const source = searchParams.get("source");
    if (source === "price-proposal") {
      setIsPriceProposalMode(true);
    } else {
      setIsPriceProposalMode(false);
    }
  }, [searchParams]);

  const handleCartClick = () => {
    if (onCartClick) {
      onCartClick();
    } else {
      router.push("/cart");
    }
  };

  const handleAddPriceProposal = () => {
    // Проверяем авторизацию
    if (!isAuthenticated) {
      router.push("/auth?from=tender");
      return;
    }

    // Проверяем companyId
    if (
      !user?.userId ||
      user?.userId === "00000000-0000-0000-0000-000000000000" ||
      !user?.companyId ||
      user?.companyId === "0000" ||
      user?.companyId === 0
    ) {
      router.push("/auth?from=company");
      return;
    }

    router.push("/products/page/1?source=price-proposal");
  };

  const handleBackToCatalog = () => {
    // Принудительно очищаем URL от параметров и перезагружаем
    window.location.replace("/products/page/1");
  };

  const handleAddMaterial = () => {
    // Проверяем авторизацию
    if (!isAuthenticated) {
      router.push("/auth?from=tender");
      return;
    }

    // Проверяем companyId
    if (
      !user?.userId ||
      user?.userId === "00000000-0000-0000-0000-000000000000" ||
      !user?.companyId ||
      user?.companyId === "0000" ||
      user?.companyId === 0
    ) {
      router.push("/auth?from=company");
      return;
    }

    router.push("/add-material");
  };

  return (
    <HeaderContainer as="nav">
      <RegistryLink href="https://sadi.kz/">
        <img
          src="/favicon.ico"
          width={"18"}
          height={"18"}
          alt="Иконка реестра"
        />
        SADI.KZ
      </RegistryLink>

      <RightSection>
        <SupportLink href="#" aria-label="Связаться со службой поддержки">
          <img
            src="/icons/Vector.svg "
            width={"10"}
            height={"10"}
            alt="Иконка поддержки"
          />
          Служба поддержки
        </SupportLink>

        {isPriceProposalMode ? (
          <>
            <AddPriceButton
              onClick={handleBackToCatalog}
              aria-label="Вернуться к каталогу строительных материалов"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="white"
                style={{ marginRight: "6px" }}
              >
                <path d="M15.41 7.41 14 6l-6 6 6 6 1.41-1.41L10.83 12z" />
              </svg>
              Вернуться к каталогу
            </AddPriceButton>
            <AddPriceButton
              onClick={handleAddMaterial}
              aria-label="Подать заявку на добавление нового материала"
              style={{ marginLeft: "8px" }}
            >
              <img
                src="/icons/plus.svg"
                width={"18"}
                height={"18"}
                alt="Иконка плюс"
              />
              Подать заявку на добавление материала
            </AddPriceButton>
          </>
        ) : (
          <AddPriceButton
            onClick={handleAddPriceProposal}
            aria-label="Добавить ценовое предложение"
          >
            <img
              src="/icons/plus.svg"
              width={"18"}
              height={"18"}
              alt="Иконка плюс"
            />
            Добавить ценовое предложение
          </AddPriceButton>
        )}

        <CartButton
          onClick={handleCartClick}
          aria-label={`Корзина, ${getCartItemsCount()} товаров`}
        >
          <img
            src="/icons/mainbusket.svg"
            width={"26"}
            height={"26"}
            alt="Иконка корзины"
          />
          {getCartItemsCount() > 0 && (
            <span aria-hidden="true">{getCartItemsCount()}</span>
          )}
        </CartButton>
      </RightSection>
    </HeaderContainer>
  );
};

Header.displayName = "Header";

export default Header;
